package data

import (
	"context"
	"database/sql"
	"fmt"
	stdhttp "net/http"
	"strings"
	"sync"
	"time"

	entsql "entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/schema"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	_ "github.com/lib/pq"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/adsense/v2"
	analyticsadmin "google.golang.org/api/analyticsadmin/v1alpha"
	analyticsdata "google.golang.org/api/analyticsdata/v1beta"
	"google.golang.org/api/option"
	"google.golang.org/api/transport/http"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channelcountryadformathkdpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/channelcountryadformatpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/gamecountryhkdpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/gamecountrypartition"
	_ "git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/runtime"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/siteadformathistory"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitebusiness"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitecountryadformathkdpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/sitecountryadformatpartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/urlchannelcountryadformatinfopartition"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/data/ent/urlchannelcountryadformatinfopartitionhkd"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData, NewBotRepo, NewGoogleServiceManager, NewAnalyticsService, NewAdsenseRepo, NewChannelCountryRepo, NewSiteRepo,
	NewSiteAdFormatRepo, NewUrlChannelCountryRepo, NewTodaySiteCountryRepo, NewSiteCountryAdFormatPartitionRepo, NewUrlChannelCountryAdFormatRepo,
	NewEntClient, NewSiteCountryRepo, NewPageURLRepo, NewTodaySiteRepo, NewChannelCountryAdFormatPartitionRepo, NewCooperationRepo, NewUnifiedSiteRepo, //
	NewUrlChannelRepo, NewChannelRepo, NewUrlChannelAdFormatRepo, NewGameRepo, NewCustomChannelRepo, NewCooperationSiteUrlChannelRepo, NewAnalyticsRepo,
	NewChannelAdFormatRepo, NewPageURLAdFormatRepo, NewShardingTablesRepo, NewGameCountryRepo, NewAppCountrySummaryRepo, NewAdManagerSiteRepo, NewAnalyticsSiteRepo,
	NewJobLogRepo, NewTgAdRepo, NewCountryRepo, NewAdReductionRepo, NewPurchaseRepo, NewGamePurchaseSummaryRepo, NewSiteAdUnitRepo, NewPageURLCountryRepo, NewBusinessSiteRepo, NewDeploymentHistoryRepo,
	NewPageURLCountryAdFormatRepo,
)

type GoogleService struct {
	adsense *adsense.Service
	client  *stdhttp.Client
	pubID   string
}

type AnalyticsService struct {
	analytics      *analyticsdata.Service
	analyticsAdmin *analyticsadmin.Service
}

type GoogleServiceManager struct {
	services  map[string]*GoogleService     // pubID -> GoogleService
	configs   map[string]*conf.GoogleConfig // pubID -> GoogleConfig
	analytics *AnalyticsService             // 独立的Analytics服务
	mu        sync.RWMutex
}

func NewGoogleServiceManager(googleConf *conf.Google, logger log.Logger) (*GoogleServiceManager, error) {
	manager := &GoogleServiceManager{
		services: make(map[string]*GoogleService),
		configs:  make(map[string]*conf.GoogleConfig),
	}

	ctx := context.Background()

	// 创建独立的Analytics服务
	hlog := log.NewHelper(logger)
	analyticsService, err := createAnalyticsService(ctx)
	if err != nil {
		panic(err)
	}
	manager.analytics = analyticsService
	hlog.Info("成功创建Analytics服务")

	// 为每个配置创建AdSense服务
	for _, config := range googleConf.GetConfigs() {
		// 检查必要字段
		if config.PubID == "" {
			hlog.Warnf("跳过配置：PubID为空，账号名称: %s", config.AccountName)
			continue
		}

		// 检查是否启用
		if !config.Enabled {
			hlog.Infof("跳过已禁用的账号：%s (PubID: %s)", config.AccountName, config.PubID)
			continue
		}

		service, err := createGoogleService(ctx, config)
		if err != nil {
			panic(err)
		}

		// 使用PubID作为key
		manager.services[config.PubID] = service
		manager.configs[config.PubID] = config
		hlog.Infof("成功创建GoogleService，账号: %s, PubID: %s",
			config.AccountName, config.PubID)
	}

	if len(manager.services) == 0 {
		panic(err)
	}

	return manager, nil
}

func (m *GoogleServiceManager) GetService(pubID string) (*GoogleService, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	service, exists := m.services[pubID]
	if !exists {
		return nil, fmt.Errorf("未找到PubID为 %s 的GoogleService", pubID)
	}
	return service, nil
}

func (m *GoogleServiceManager) GetConfig(pubID string) (*conf.GoogleConfig, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	config, exists := m.configs[pubID]
	if !exists {
		return nil, fmt.Errorf("未找到PubID为 %s 的配置", pubID)
	}
	return config, nil
}

func (m *GoogleServiceManager) GetAllPubIDs() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	pubIDs := make([]string, 0, len(m.services))
	for pubID := range m.services {
		pubIDs = append(pubIDs, pubID)
	}
	return pubIDs
}

func (m *GoogleServiceManager) GetAnalyticsService() *AnalyticsService {
	m.mu.RLock()
	defer m.mu.RUnlock()

	return m.analytics
}

func (m *GoogleServiceManager) GetEnabledPubIDs() []string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	pubIDs := make([]string, 0, len(m.services))
	for pubID, _ := range m.services {
		pubIDs = append(pubIDs, pubID)
	}
	return pubIDs
}

func (m *GoogleServiceManager) GetAccountInfo(pubID string) (*conf.GoogleConfig, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	config, exists := m.configs[pubID]
	if !exists {
		return nil, fmt.Errorf("未找到PubID为 %s 的账号配置", pubID)
	}
	return config, nil
}

func createGoogleService(ctx context.Context, config *conf.GoogleConfig) (*GoogleService, error) {
	var oauthConfig = &oauth2.Config{
		ClientID:     config.ClientID,
		ClientSecret: config.ClientSecret,
		Endpoint:     google.Endpoint,
		Scopes:       []string{adsense.AdsenseReadonlyScope},
		RedirectURL:  "https://developers.google.com",
	}

	var options []option.ClientOption
	if config.UseSa {
		options = []option.ClientOption{
			option.WithScopes(adsense.AdsenseReadonlyScope),
			option.WithCredentialsFile("./configs/sa.json"),
		}
	} else {
		options = []option.ClientOption{
			option.WithScopes(adsense.AdsenseReadonlyScope),
			option.WithTokenSource(oauthConfig.TokenSource(ctx, &oauth2.Token{RefreshToken: config.Code})),
		}
	}

	client, _, err := http.NewClient(ctx, options...)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP客户端失败: %w", err)
	}

	adsenseService, err := adsense.NewService(ctx, options...)
	if err != nil {
		return nil, fmt.Errorf("创建AdSense服务失败: %w", err)
	}

	return &GoogleService{
		adsense: adsenseService,
		client:  client,
		pubID:   config.PubID,
	}, nil
}

func createAnalyticsService(ctx context.Context) (*AnalyticsService, error) {
	gaSaOption := option.WithCredentialsFile("./configs/service_account.json")

	analyticsService, err := analyticsdata.NewService(ctx, option.WithScopes(analyticsdata.AnalyticsReadonlyScope), gaSaOption)
	if err != nil {
		return nil, fmt.Errorf("创建Analytics服务失败: %w", err)
	}

	analyticsAdminService, err := analyticsadmin.NewService(ctx, option.WithScopes(analyticsadmin.AnalyticsReadonlyScope), gaSaOption)
	if err != nil {
		return nil, fmt.Errorf("创建AnalyticsAdmin服务失败: %w", err)
	}

	return &AnalyticsService{
		analytics:      analyticsService,
		analyticsAdmin: analyticsAdminService,
	}, nil
}

// NewAnalyticsService 创建独立的Analytics服务
func NewAnalyticsService(logger log.Logger) (*AnalyticsService, error) {
	ctx := context.Background()
	return createAnalyticsService(ctx)
}

// Data .
type Data struct {
	log       *log.Helper
	db        *ent.Client
	stddb     *sql.DB
	gsm       *GoogleServiceManager
	analytics *AnalyticsService
}

// NewData .
func NewData(c *conf.Data, db *ent.Client, gsm *GoogleServiceManager, analytics *AnalyticsService, logger log.Logger) (*Data, func(), error) {
	cleanup := func() {
		log.NewHelper(logger).Info("closing the data resources")
	}
	user, host, port, dbname, pwd := decodeSource(c.GetDatabase().GetSource())

	stddb, err := sql.Open("postgres", fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable", user, pwd, host, port, dbname))
	//stddb, err := sql.Open("postgres", "postgres://postgres:123456@localhost/minigameData?sslmode=disable")
	if err != nil {
		log.Fatal(err)
	}
	stddb.SetConnMaxLifetime(time.Hour)
	stddb.SetMaxOpenConns(30)

	return &Data{
		gsm:       gsm,
		analytics: analytics,
		db:        db,
		stddb:     stddb,
		log:       log.NewHelper(log.With(logger, "module", "data")),
	}, cleanup, nil
}

func NewEntClient(conf *conf.Data, logger log.Logger) *ent.Client {
	hlog := log.NewHelper(log.With(logger, "module", "adsense-bot/data/ent"))

	// Create an ent.Driver from `db`.
	drv, err := entsql.Open(conf.Database.Driver, conf.Database.Source)
	if err != nil {
		hlog.Fatalf("failed opening connection to postgres: %v", err)
		panic(err)
	}
	db := drv.DB()
	db.SetConnMaxLifetime(time.Hour)
	db.SetMaxOpenConns(10)
	client := ent.NewClient(ent.Driver(drv))
	// Run the auto migration tool.
	//if err := client.Schema.Create(context.Background(), migrate.WithForeignKeys(false)); err != nil {
	//	hlog.Fatalf("failed creating schema resources: %v", err)
	//	panic(err)
	//}
	if err := client.Schema.Create(
		context.Background(),
		schema.WithHooks(func(next schema.Creator) schema.Creator {
			return schema.CreateFunc(func(ctx context.Context, tables ...*schema.Table) error {
				var dynamicTables []*schema.Table
				for _, v := range tables {
					if !isPartitionTable(v.Name) { // todo 待其他表拆分后，删除
						nv := &schema.Table{
							Name:        v.Name,
							Columns:     v.Columns,
							Indexes:     v.Indexes,
							PrimaryKey:  v.PrimaryKey,
							ForeignKeys: v.ForeignKeys,
							Annotation:  v.Annotation,
							Comment:     v.Comment,
						}
						dynamicTables = append(dynamicTables, nv)
					} else {
						//todo create channelcountryadformatpartition
					}
				}
				return next.Create(ctx, dynamicTables...)
			})
		}),
	); err != nil {
		hlog.Fatalf("failed creating schema resources: %v", err)
	}
	return client
}

func isPartitionTable(name string) bool {
	return name == channelcountryadformatpartition.Table ||
		name == sitecountryadformatpartition.Table ||
		name == urlchannelcountryadformatinfopartition.Table ||
		name == gamecountrypartition.Table ||
		name == channelcountryadformathkdpartition.Table ||
		name == sitecountryadformathkdpartition.Table ||
		name == urlchannelcountryadformatinfopartitionhkd.Table ||
		name == gamecountryhkdpartition.Table || name == siteadformathistory.Table || name == sitebusiness.Table
}

func decodeSource(value string) (user, host, port, dbname, pwd string) {
	// 分割字符串为键值对
	pairs := strings.Split(value, " ")
	// 解析键值对
	result := make(map[string]string)
	for _, pair := range pairs {
		parts := strings.SplitN(pair, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			val := strings.TrimSpace(parts[1])
			result[key] = val
		}
	}
	return result["user"], result["host"], result["port"], result["dbname"], result["password"]
}

func (d *Data) SaveLog(ctx context.Context, jobLog *biz.JobLog) error {
	err := d.db.JobLog.Create().SetJobName(jobLog.Name).SetIsSuccess(jobLog.IsSuccess).SetMessage(jobLog.Message).
		SetJobTime(jobLog.Date).SetDateRange(jobLog.DateRange).SetCurrencyCode(jobLog.CurrencyCode).Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}
